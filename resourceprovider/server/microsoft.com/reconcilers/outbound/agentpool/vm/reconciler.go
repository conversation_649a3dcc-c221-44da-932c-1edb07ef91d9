package vm

import (
	"context"
	"fmt"
	"strings"

	"github.com/Azure/azure-sdk-for-go/services/compute/mgmt/2022-03-01/compute"
	"github.com/Azure/go-autorest/autorest/to"

	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool/vm/nic"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/common/vmhelper"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/tags"
	"go.goms.io/aks/rp/toolkit/apierror"
	"go.goms.io/aks/rp/toolkit/azureclients/networkinterfaceclient"
	"go.goms.io/aks/rp/toolkit/azureclients/vmclient"
	cgerror "go.goms.io/aks/rp/toolkit/categorizederror"
	"go.goms.io/aks/rp/toolkit/log"
)

type VMAgentpoolBackendpoolReconciler struct {
	vmClient      vmclient.Interface
	nicReconciler nic.NicInterfaceReconciler
}

func New(vmClient vmclient.Interface, interfaceClient networkinterfaceclient.Interface) agentpool.AgentPoolLBBackendpoolReconciler {
	return &VMAgentpoolBackendpoolReconciler{
		vmClient:      vmClient,
		nicReconciler: nic.New(interfaceClient),
	}
}

func (c *VMAgentpoolBackendpoolReconciler) DecoupleLBBackendPool(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}) *cgerror.CategorizedError {
	ctx, span := log.StartSpan(ctx, "DecoupleLBBackendPool", log.AKSTeamNetworkIntegration)
	defer span.End()
	nicMap, cerr := c.listAllOfVMInterfaces(ctx, computeSubscriptionID, resourceGroupName)
	if cerr != nil {
		return cerr
	}
	return c.nicReconciler.DecoupleNicInterfaceWithLBBackendpool(ctx, networkSubscriptionID, resourceGroupName, nicMap, backendpoolIDs, backendpoolIDsIPV6)
}

func (c *VMAgentpoolBackendpoolReconciler) AssociateLBBackendpool(ctx context.Context, computeSubscriptionID, networkSubscriptionID, resourceGroupName string, backendpoolIDs map[string]struct{}, backendpoolIDsIPV6 map[string]struct{}, excludedAgentPoolNames map[string]struct{}) *cgerror.CategorizedError {
	// Get all VMs and their NICs, categorized by exclusion status
	normalNicMap, excludedNicMap, cerr := c.listAllOfVMInterfacesWithExclusionCategories(ctx, computeSubscriptionID, resourceGroupName, excludedAgentPoolNames)
	if cerr != nil {
		return cerr
	}

	// Normal VMs (not excluded) can join all backend pools
	if len(normalNicMap) > 0 {
		if err := c.nicReconciler.AssociateNicInterfaceWithLBBackendpool(ctx, networkSubscriptionID, resourceGroupName, normalNicMap, backendpoolIDs, backendpoolIDsIPV6); err != nil {
			return err
		}
	}

	// Excluded VMs can only join outbound backend pools
	if len(excludedNicMap) > 0 {
		filteredBackendpoolIDs := agentpool.FilterBackendPoolsForExclusion(ctx, backendpoolIDs, true)
		filteredBackendpoolIDsIPV6 := agentpool.FilterBackendPoolsForExclusion(ctx, backendpoolIDsIPV6, true)
		if err := c.nicReconciler.AssociateNicInterfaceWithLBBackendpool(ctx, networkSubscriptionID, resourceGroupName, excludedNicMap, filteredBackendpoolIDs, filteredBackendpoolIDsIPV6); err != nil {
			return err
		}
	}

	return nil
}

func (c *VMAgentpoolBackendpoolReconciler) listAllOfVMInterfaces(ctx context.Context, computeSubscriptionID string, resourceGroupName string) (map[string]struct{}, *cgerror.CategorizedError) {
	normalNicMap, excludedNicMap, cerr := c.listAllOfVMInterfacesWithExclusionCategories(ctx, computeSubscriptionID, resourceGroupName, nil)
	if cerr != nil {
		return nil, cerr
	}

	// If both maps are nil or empty, return nil to maintain backward compatibility
	if len(normalNicMap) == 0 && len(excludedNicMap) == 0 {
		return nil, nil
	}

	// Merge the two nic lists to get all nics
	allNicMap := make(map[string]struct{})
	for nicID := range normalNicMap {
		allNicMap[nicID] = struct{}{}
	}
	for nicID := range excludedNicMap {
		allNicMap[nicID] = struct{}{}
	}

	return allNicMap, nil
}

func (c *VMAgentpoolBackendpoolReconciler) listAllOfVMInterfacesWithExclusionCategories(ctx context.Context, computeSubscriptionID string, resourceGroupName string, excludedAgentPoolNames map[string]struct{}) (map[string]struct{}, map[string]struct{}, *cgerror.CategorizedError) {
	vmList, _, err := c.vmClient.ListVirtualMachinesWithInstanceView(ctx, computeSubscriptionID, resourceGroupName, true)
	if err != nil {
		return nil, nil, err
	}
	var normalNicMap, excludedNicMap map[string]struct{}
	for _, vm := range vmList {
		poolNameTag := tags.GetTagValueWithFallbackToOldKey(vm.Tags, tags.PoolName, tags.OldPoolName)
		if poolNameTag == nil {
			log.GetLogger(ctx).Warning(ctx, "vm is not associated with pool")
			continue
		}

		// 1. Use poolNameTag to determine if the VM is in excluded list
		var isExcludedAgentPool bool
		if excludedAgentPoolNames != nil {
			if _, excluded := excludedAgentPoolNames[*poolNameTag]; excluded {
				isExcludedAgentPool = true
				log.GetLogger(ctx).Infof(ctx, "VM %s belongs to excluded agent pool %s - will only join outbound pools", to.String(vm.Name), *poolNameTag)
			}
		}

		if vm.VirtualMachineProperties == nil || vm.VirtualMachineProperties.NetworkProfile == nil ||
			vm.VirtualMachineProperties.NetworkProfile.NetworkInterfaces == nil || len(*vm.VirtualMachineProperties.NetworkProfile.NetworkInterfaces) == 0 {
			log.GetLogger(ctx).Warning(ctx, "vm is not associated with nic")
			continue
		}
		if vm.ProvisioningState != nil && strings.EqualFold(*vm.ProvisioningState, "Deallocating") {
			err := fmt.Errorf("listAllOfVMInterfaces: found virtual machine %s being deleted, will retry later", to.String(vm.Name))
			cerr := cgerror.NewCategorizedError(ctx, apierror.ClientError, cgerror.FailedVMIsBeingDeleted, cgerror.VirtualMachines, err)
			log.GetLogger(ctx).Warning(ctx, cerr.Error())
			continue
		}
		// ARG team support intanceView expand with sp 1st party token, but not support msi
		// to be more robust, if the instanceview is nil, need to call getInstanceView for each vm
		// instanceView, cerr := vmClient.GetVirtualMachineInstanceView(ctx, subscriptionID, managedCluster.NodeResourceGroup, *vm.Name)
		if vm.InstanceView == nil {
			var cerr *cgerror.CategorizedError
			vm.InstanceView, cerr = c.vmClient.GetVirtualMachineInstanceView(ctx, computeSubscriptionID, resourceGroupName, *vm.Name)
			if cerr != nil {
				return nil, nil, cerr
			}
		}
		if vm.InstanceView != nil && vmhelper.DeallocatedAndStoppedVMFilter(ctx, vm.InstanceView) {
			log.GetLogger(ctx).Warning(ctx, "vm is deallocated or stopped")
			continue
		}

		nicID := getVMPrimaryNetworkInterfaceConfiguration(*vm.VirtualMachineProperties.NetworkProfile.NetworkInterfaces)
		if nicID == nil {
			log.GetLogger(ctx).Warning(ctx, "failed to find primary nic")
			continue
		}

		// 2. If not excluded, add the VM to normal list
		// 3. If excluded, add the VM to excluded list
		if isExcludedAgentPool {
			if excludedNicMap == nil {
				excludedNicMap = make(map[string]struct{})
			}
			excludedNicMap[strings.ToLower(*nicID)] = struct{}{}
		} else {
			if normalNicMap == nil {
				normalNicMap = make(map[string]struct{})
			}
			normalNicMap[strings.ToLower(*nicID)] = struct{}{}
		}
	}
	return normalNicMap, excludedNicMap, nil
}

func getVMPrimaryNetworkInterfaceConfiguration(networkConfigurations []compute.NetworkInterfaceReference) *string {
	if len(networkConfigurations) == 1 {
		return networkConfigurations[0].ID
	}

	for idx := range networkConfigurations {
		networkConfig := networkConfigurations[idx]
		if networkConfig.NetworkInterfaceReferenceProperties != nil && networkConfig.Primary != nil && *networkConfig.Primary {
			return networkConfig.ID
		}
	}

	return nil
}
